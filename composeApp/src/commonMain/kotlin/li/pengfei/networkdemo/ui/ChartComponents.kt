package li.pengfei.networkdemo.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import li.pengfei.networkdemo.data.CategorizedChartData
import li.pengfei.networkdemo.data.ChartDataPoint
import kotlin.math.max
import kotlin.math.min

/**
 * Card wrapper for charts with title and content
 */
@Composable
fun ChartCard(
    title: String,
    chartData: CategorizedChartData,
    chartType: ChartType,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            if (chartData.dataPoints.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                when (chartType) {
                    ChartType.SCATTER -> ScatterPlotChart(chartData)
                    ChartType.HISTOGRAM -> HistogramChart(chartData)
                    ChartType.LINE -> LineChart(chartData)
                }
                
                // Legend
                if (chartData.categories.size > 1) {
                    ChartLegend(
                        categories = chartData.categories.toList(),
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }
    }
}

/**
 * Scatter plot chart component
 */
@Composable
private fun ScatterPlotChart(chartData: CategorizedChartData) {
    val categoryColors = remember(chartData.categories) {
        generateCategoryColors(chartData.categories.size)
    }

    if (chartData.dataPoints.isNotEmpty()) {
        SimpleChart(
            chartData = chartData,
            chartType = ChartType.SCATTER,
            colors = categoryColors
        )
    }
}

/**
 * Histogram chart component
 */
@Composable
private fun HistogramChart(chartData: CategorizedChartData) {
    val categoryColors = remember(chartData.categories) {
        generateCategoryColors(chartData.categories.size)
    }

    if (chartData.dataPoints.isNotEmpty()) {
        SimpleChart(
            chartData = chartData,
            chartType = ChartType.HISTOGRAM,
            colors = categoryColors
        )
    }
}

/**
 * Line chart component for PDF and CDF
 */
@Composable
private fun LineChart(chartData: CategorizedChartData) {
    val categoryColors = remember(chartData.categories) {
        generateCategoryColors(chartData.categories.size)
    }

    if (chartData.dataPoints.isNotEmpty()) {
        SimpleChart(
            chartData = chartData,
            chartType = ChartType.LINE,
            colors = categoryColors
        )
    }
}

/**
 * Simple chart component using Canvas
 */
@Composable
private fun SimpleChart(
    chartData: CategorizedChartData,
    chartType: ChartType,
    colors: List<Color>,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // Chart area
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .background(Color.White)
        ) {
            drawSimpleChart(chartData, chartType, colors)
        }

        // Axis labels
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = chartData.yAxisLabel,
                style = MaterialTheme.typography.labelSmall,
                modifier = Modifier.padding(start = 16.dp)
            )
            Text(
                text = chartData.xAxisLabel,
                style = MaterialTheme.typography.labelSmall,
                modifier = Modifier.padding(end = 16.dp)
            )
        }
    }
}

/**
 * Chart legend component
 */
@Composable
private fun ChartLegend(
    categories: List<String>,
    modifier: Modifier = Modifier
) {
    val colors = remember {
        generateCategoryColors(categories.size)
    }

    Column(modifier = modifier) {
        Text(
            text = "Legend:",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 4.dp)
        )

        categories.forEachIndexed { index, category ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 2.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .padding(end = 8.dp)
                ) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = colors[index % colors.size]
                        ),
                        modifier = Modifier.fillMaxSize()
                    ) {}
                }
                Text(
                    text = category,
                    style = MaterialTheme.typography.labelSmall
                )
            }
        }
    }
}

/**
 * Draw simple chart using Canvas
 */
private fun DrawScope.drawSimpleChart(
    chartData: CategorizedChartData,
    chartType: ChartType,
    colors: List<Color>
) {
    if (chartData.dataPoints.isEmpty()) return

    val padding = 40f
    val chartWidth = size.width - 2 * padding
    val chartHeight = size.height - 2 * padding

    // Group data by category
    val groupedData = chartData.dataPoints.groupBy { it.category }

    // Find min/max values for scaling
    val allYValues = chartData.dataPoints.map { it.y }
    val minY = allYValues.minOrNull() ?: 0f
    val maxY = allYValues.maxOrNull() ?: 1f
    val yRange = maxY - minY

    val allXValues = chartData.dataPoints.mapNotNull { it.x.toFloatOrNull() }
    val minX = allXValues.minOrNull() ?: 0f
    val maxX = allXValues.maxOrNull() ?: 1f
    val xRange = maxX - minX

    // Draw axes
    drawLine(
        color = Color.Black,
        start = Offset(padding, padding),
        end = Offset(padding, size.height - padding),
        strokeWidth = 2f
    )
    drawLine(
        color = Color.Black,
        start = Offset(padding, size.height - padding),
        end = Offset(size.width - padding, size.height - padding),
        strokeWidth = 2f
    )

    // Draw data points
    groupedData.entries.forEachIndexed { categoryIndex, (category, points) ->
        val color = colors[categoryIndex % colors.size]

        when (chartType) {
            ChartType.SCATTER -> {
                points.forEach { point ->
                    val x = if (xRange > 0) {
                        padding + (point.x.toFloatOrNull()?.minus(minX) ?: 0f) / xRange * chartWidth
                    } else {
                        padding + categoryIndex * chartWidth / groupedData.size
                    }
                    val y = if (yRange > 0) {
                        size.height - padding - (point.y - minY) / yRange * chartHeight
                    } else {
                        size.height - padding - chartHeight / 2
                    }

                    drawCircle(
                        color = color,
                        radius = 4f,
                        center = Offset(x, y)
                    )
                }
            }
            ChartType.HISTOGRAM -> {
                val barWidth = chartWidth / points.size
                points.forEachIndexed { index, point ->
                    val x = padding + index * barWidth
                    val barHeight = if (yRange > 0) {
                        (point.y - minY) / yRange * chartHeight
                    } else {
                        chartHeight / 2
                    }
                    val y = size.height - padding - barHeight

                    drawRect(
                        color = color,
                        topLeft = Offset(x, y),
                        size = androidx.compose.ui.geometry.Size(barWidth * 0.8f, barHeight)
                    )
                }
            }
            ChartType.LINE -> {
                val sortedPoints = points.sortedBy { it.x.toFloatOrNull() ?: 0f }
                for (i in 0 until sortedPoints.size - 1) {
                    val point1 = sortedPoints[i]
                    val point2 = sortedPoints[i + 1]

                    val x1 = if (xRange > 0) {
                        padding + (point1.x.toFloatOrNull()?.minus(minX) ?: 0f) / xRange * chartWidth
                    } else {
                        padding + i * chartWidth / sortedPoints.size
                    }
                    val y1 = if (yRange > 0) {
                        size.height - padding - (point1.y - minY) / yRange * chartHeight
                    } else {
                        size.height - padding - chartHeight / 2
                    }

                    val x2 = if (xRange > 0) {
                        padding + (point2.x.toFloatOrNull()?.minus(minX) ?: 0f) / xRange * chartWidth
                    } else {
                        padding + (i + 1) * chartWidth / sortedPoints.size
                    }
                    val y2 = if (yRange > 0) {
                        size.height - padding - (point2.y - minY) / yRange * chartHeight
                    } else {
                        size.height - padding - chartHeight / 2
                    }

                    drawLine(
                        color = color,
                        start = Offset(x1, y1),
                        end = Offset(x2, y2),
                        strokeWidth = 2f
                    )
                }
            }
        }
    }
}

/**
 * Generate distinct colors for categories
 */
private fun generateCategoryColors(count: Int): List<Color> {
    val baseColors = listOf(
        Color(0xFF1f77b4), // Blue
        Color(0xFFff7f0e), // Orange
        Color(0xFF2ca02c), // Green
        Color(0xFFd62728), // Red
        Color(0xFF9467bd), // Purple
        Color(0xFF8c564b), // Brown
        Color(0xFFe377c2), // Pink
        Color(0xFF7f7f7f), // Gray
        Color(0xFFbcbd22), // Olive
        Color(0xFF17becf)  // Cyan
    )
    
    return (0 until count).map { index ->
        baseColors[index % baseColors.size]
    }
}
