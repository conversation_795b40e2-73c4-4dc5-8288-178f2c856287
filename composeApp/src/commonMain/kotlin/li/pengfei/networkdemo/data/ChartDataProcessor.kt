package li.pengfei.networkdemo.data

/**
 * Enhanced data processor for generating comprehensive chart datasets
 * with proper categorization for different network technologies and bands
 */
class ChartDataProcessor {
    
    /**
     * Generate enhanced device state chart data with proper time formatting
     */
    fun getEnhancedDeviceStateData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Device State Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Device State (1=Free, 2=Head, 3=Body)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = records.mapIndexed { index, record ->
            ChartDataPoint(
                x = index.toString(), // Use index for better chart rendering
                y = record.deviceState.value.toFloat(),
                category = "Device State",
                subcategory = record.deviceState.displayName
            )
        }

        return CategorizedChartData(
            title = "Device State Over Time",
            xAxisLabel = "Time Index",
            yAxisLabel = "Device State (1=Free, 2=Head, 3=Body)",
            dataPoints = dataPoints,
            categories = setOf("Device State"),
            subcategories = setOf("Free", "Head", "Body")
        )
    }
    
    /**
     * Generate enhanced RSRP chart data with technology and band separation
     */
    fun getEnhancedRsrpData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "RSRP Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "RSRP (dBm)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()
        val categories = mutableSetOf<String>()
        val subcategories = mutableSetOf<String>()

        records.forEachIndexed { timeIndex, record ->
            record.cells.forEach { cell ->
                cell.rsrpValues.forEachIndexed { rsrpIndex, rsrp ->
                    if (rsrp.isFinite() && rsrp != 0.0) {
                        val category = "${cell.rat.displayName}_Band${cell.band}"
                        val subcategory = "RSRP${rsrpIndex}"
                        
                        categories.add(category)
                        subcategories.add(subcategory)
                        
                        dataPoints.add(
                            ChartDataPoint(
                                x = timeIndex.toString(),
                                y = rsrp.toFloat(),
                                category = category,
                                subcategory = subcategory
                            )
                        )
                    }
                }
            }
        }

        return CategorizedChartData(
            title = "RSRP Over Time by Technology and Band",
            xAxisLabel = "Time Index",
            yAxisLabel = "RSRP (dBm)",
            dataPoints = dataPoints,
            categories = categories,
            subcategories = subcategories
        )
    }
    
    /**
     * Generate enhanced Tx Power chart data with technology and band separation
     */
    fun getEnhancedTxPowerData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Tx Power Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Tx Power (dBm)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()
        val categories = mutableSetOf<String>()
        val subcategories = mutableSetOf<String>()

        records.forEachIndexed { timeIndex, record ->
            record.cells.forEach { cell ->
                cell.txPowerValues.forEachIndexed { txIndex, txPower ->
                    if (txPower.isFinite() && txPower != 0.0) {
                        val category = "${cell.rat.displayName}_Band${cell.band}"
                        val subcategory = "TxPower${txIndex}"
                        
                        categories.add(category)
                        subcategories.add(subcategory)
                        
                        dataPoints.add(
                            ChartDataPoint(
                                x = timeIndex.toString(),
                                y = txPower.toFloat(),
                                category = category,
                                subcategory = subcategory
                            )
                        )
                    }
                }
            }
        }

        return CategorizedChartData(
            title = "Tx Power Over Time by Technology and Band",
            xAxisLabel = "Time Index",
            yAxisLabel = "Tx Power (dBm)",
            dataPoints = dataPoints,
            categories = categories,
            subcategories = subcategories
        )
    }
    
    /**
     * Generate enhanced band chart data with color-coded bands
     */
    fun getEnhancedBandData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Bands Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Band",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()
        val bandToNumber = mutableMapOf<String, Float>()
        val categories = mutableSetOf<String>()
        val subcategories = mutableSetOf<String>()
        var bandCounter = 1f

        records.forEachIndexed { timeIndex, record ->
            record.cells.forEach { cell ->
                if (cell.band.isNotEmpty()) {
                    val bandNumber = bandToNumber.getOrPut(cell.band) { bandCounter++ }
                    val category = cell.band
                    val subcategory = cell.rat.displayName
                    
                    categories.add(category)
                    subcategories.add(subcategory)
                    
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeIndex.toString(),
                            y = bandNumber,
                            category = category,
                            subcategory = subcategory
                        )
                    )
                }
            }
        }

        return CategorizedChartData(
            title = "Frequency Bands Over Time",
            xAxisLabel = "Time Index",
            yAxisLabel = "Band (Encoded)",
            dataPoints = dataPoints,
            categories = categories,
            subcategories = subcategories
        )
    }
    
    /**
     * Generate enhanced channel chart data separated by technology
     */
    fun getEnhancedChannelData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Channels Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Channel",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()
        val categories = mutableSetOf<String>()
        val subcategories = mutableSetOf<String>()

        records.forEachIndexed { timeIndex, record ->
            record.cells.forEach { cell ->
                // Downlink channels
                cell.dlChannel?.let { dlChannel ->
                    val category = "${cell.rat.displayName}_Band${cell.band}"
                    val subcategory = "DL_Channel"
                    
                    categories.add(category)
                    subcategories.add(subcategory)
                    
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeIndex.toString(),
                            y = (dlChannel / 1000f), // Scale down for better visualization
                            category = category,
                            subcategory = subcategory
                        )
                    )
                }
                
                // Uplink channels
                cell.ulChannel?.let { ulChannel ->
                    val category = "${cell.rat.displayName}_Band${cell.band}"
                    val subcategory = "UL_Channel"
                    
                    categories.add(category)
                    subcategories.add(subcategory)
                    
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeIndex.toString(),
                            y = (ulChannel / 1000f), // Scale down for better visualization
                            category = category,
                            subcategory = subcategory
                        )
                    )
                }
            }
        }

        return CategorizedChartData(
            title = "Channels Over Time by Technology",
            xAxisLabel = "Time Index",
            yAxisLabel = "Channel (scaled by 1000)",
            dataPoints = dataPoints,
            categories = categories,
            subcategories = subcategories
        )
    }
    
    /**
     * Generate enhanced throughput chart data
     */
    fun getEnhancedThroughputData(records: List<NetworkRecord>, isUplink: Boolean): CategorizedChartData {
        if (records.isEmpty()) {
            val direction = if (isUplink) "Uplink" else "Downlink"
            return CategorizedChartData(
                title = "$direction Throughput Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Throughput (Kb/s)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = records.mapIndexed { timeIndex, record ->
            val throughput = if (isUplink) record.ulThroughput else record.dlThroughput
            val direction = if (isUplink) "Uplink" else "Downlink"
            
            ChartDataPoint(
                x = timeIndex.toString(),
                y = throughput.toFloat(),
                category = direction,
                subcategory = "Throughput"
            )
        }

        val direction = if (isUplink) "Uplink" else "Downlink"
        return CategorizedChartData(
            title = "$direction Throughput Over Time",
            xAxisLabel = "Time Index",
            yAxisLabel = "Throughput (Kb/s)",
            dataPoints = dataPoints,
            categories = setOf(direction),
            subcategories = setOf("Throughput")
        )
    }
    
    /**
     * Generate all enhanced chart data types
     */
    fun generateAllEnhancedChartData(records: List<NetworkRecord>): List<CategorizedChartData> {
        return listOf(
            getEnhancedDeviceStateData(records),
            getEnhancedRsrpData(records),
            getEnhancedTxPowerData(records),
            getEnhancedBandData(records),
            getEnhancedChannelData(records),
            getEnhancedThroughputData(records, isUplink = true),
            getEnhancedThroughputData(records, isUplink = false)
        )
    }
}
