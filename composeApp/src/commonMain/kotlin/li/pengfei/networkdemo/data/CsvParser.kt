package li.pengfei.networkdemo.data

import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.format.byUnicodePattern
import kotlinx.datetime.toInstant
import kotlinx.serialization.json.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * CSV parser for network data
 */
class CsvParser {
    
    private val dateTimeFormat = LocalDateTime.Format {
        byUnicodePattern("yyyy/M/d H:mm")
    }
    
    /**
     * Parse CSV content into a list of NetworkRecord objects
     */
    fun parseNetworkData(csvContent: String): List<NetworkRecord> {
        // Step 1: Convert CSV to JSON array
        val jsonArray = csvToJsonArray(csvContent)
        println("jsonArray.size: ${jsonArray.size}")
        // Step 2: Process JSON array to NetworkRecord objects
        return jsonArray.mapNotNull { jsonObject ->
            try {
                println("jsonObject: ${Json.encodeToString(jsonObject)}")
                parseNetworkRecordFromJson(jsonObject).apply {
                    println("parsed record: $this")
                }
            } catch (e: Exception) {
                println("Error parsing record: ${e.message}")
                null
            }
        }.apply {
            println("parsed ${this.size} records")
        }
    }

    /**
     * Convert CSV content to JSON array
     */
    private fun csvToJsonArray(csvContent: String): List<JsonObject> {
        val lines = csvContent.lines().filter { it.isNotBlank() }
        if (lines.isEmpty()) return emptyList()

        // Get headers from first line
        val headers = lines.first().split(",")

        // Process data lines
        val dataLines = lines.drop(1)
        println("dataLines.size: ${dataLines.size}")

        return dataLines.map { line ->
            val values = line.split(",")
            val jsonObject = buildJsonObject {
                headers.forEachIndexed { index, header ->
                    val value = if (index < values.size) values[index] else ""
                    put(header, JsonPrimitive(value))
                }
            }
            jsonObject
        }
    }


    /**
     * Parse NetworkRecord from JSON object
     */
    private fun parseNetworkRecordFromJson(jsonObject: JsonObject): NetworkRecord? {
        try {
            val timeStr = jsonObject["Time"]?.jsonPrimitive?.content ?: ""
            val time = LocalDateTime.parse(timeStr, dateTimeFormat)
            val deviceState = DeviceState.fromString(jsonObject["DeviceState"]?.jsonPrimitive?.content ?: "")
            val ulThroughput = jsonObject["UL_TPut(Kb/s)"]?.jsonPrimitive?.doubleOrNull ?: 0.0
            val dlThroughput = jsonObject["DL_TPut(Kb/s)"]?.jsonPrimitive?.doubleOrNull ?: 0.0

            val cells = mutableListOf<CellData>()

            // Parse Cell1 data
            parseCellFromJson(jsonObject, "Cell1", 1)?.let { cells.add(it) }

            // Parse Cell2 data
            parseCellFromJson(jsonObject, "Cell2", 2)?.let { cells.add(it) }

            // Parse Cell3 data
            parseCellFromJson(jsonObject, "Cell3", 3)?.let { cells.add(it) }

            // Parse Cell4 data
            parseCellFromJson(jsonObject, "Cell4", 4)?.let { cells.add(it) }

            return NetworkRecord(
                time = time,
                deviceState = deviceState,
                ulThroughput = ulThroughput,
                dlThroughput = dlThroughput,
                cells = cells
            )
        } catch (e: Exception) {
            println("Error parsing record from JSON: ${e.message}")
            return null
        }
    }


    /**
     * Parse CellData from JSON object with prefix
     */
    private fun parseCellFromJson(jsonObject: JsonObject, prefix: String, cellId: Int): CellData? {
        try {
            val ratStr = jsonObject["RAT(${prefix})"]?.jsonPrimitive?.content?.trim()
                ?: return null

            if (ratStr.isEmpty()) return null

            val rat = NetworkTechnology.fromString(ratStr)
            val band = jsonObject["Band(${prefix})"]?.jsonPrimitive?.content?.trim() ?: ""
            val dlChannel = jsonObject["DL_Channel(${prefix})"]?.jsonPrimitive?.longOrNull
            val ulChannel = jsonObject["UL_Channel(${prefix})"]?.jsonPrimitive?.longOrNull

            // Parse RSRP values
            val rsrpValues = mutableListOf<Double>()
            for (i in 0..3) {
                jsonObject["RERP$i(${prefix})"]?.jsonPrimitive?.doubleOrNull?.let {
                    rsrpValues.add(it)
                }
            }

            // Parse TxPower values
            val txPowerValues = mutableListOf<Double>()
            for (i in 0..1) {
                jsonObject["TXPower$i(${prefix})"]?.jsonPrimitive?.doubleOrNull?.let {
                    txPowerValues.add(it)
                }
            }

            return CellData(
                cellId = cellId,
                rat = rat,
                band = band,
                dlChannel = dlChannel,
                ulChannel = ulChannel,
                rsrpValues = rsrpValues,
                txPowerValues = txPowerValues
            )
        } catch (e: Exception) {
            println("Error parsing cell $cellId from JSON: ${e.message}")
            return null
        }
    }

}

/**
 * Data processor for converting network records to chart data
 */
class NetworkDataProcessor {

    /**
     * Convert LocalDateTime to formatted time string for chart x-axis
     * This ensures all charts use the actual time from NetworkRecord.time
     */
    private fun getTimeAsString(time: LocalDateTime): String {
        // Format time as HH:mm for display
        return "${time.hour.toString().padStart(2, '0')}:${time.minute.toString().padStart(2, '0')}"
    }

    /**
     * Convert network records to device state chart data
     */
    fun getDeviceStateData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Device State Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Device State",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = records.map { record ->
            val timeString = getTimeAsString(record.time)
            ChartDataPoint(
                x = timeString,
                y = record.deviceState.value.toFloat(),
                category = "Device State" // Use single category for continuous line
            )
        }

        return CategorizedChartData(
            title = "Device State Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Device State (1=Free, 2=Head, 3=Body)",
            dataPoints = dataPoints,
            categories = setOf("Device State")
        )
    }
    
    /**
     * Convert network records to RSRP chart data
     */
    fun getRsrpData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "RSRP Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "RSRP (dBm)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()

        records.forEach { record ->
            val timeString = getTimeAsString(record.time)
            record.cells.forEach { cell ->
                cell.rsrpValues.forEachIndexed { rsrpIndex, rsrp ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeString,
                            y = rsrp.toFloat(),
                            category = "${cell.rat.displayName}_Band${cell.band}",
                            subcategory = "RSRP${rsrpIndex}"
                        )
                    )
                }
            }
        }

        return CategorizedChartData(
            title = "RSRP Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "RSRP (dBm)",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
    
    /**
     * Convert network records to TxPower chart data
     */
    fun getTxPowerData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Tx Power Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Tx Power (dBm)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()

        records.forEach { record ->
            val timeString = getTimeAsString(record.time)
            record.cells.forEach { cell ->
                cell.txPowerValues.forEachIndexed { txIndex, txPower ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeString,
                            y = txPower.toFloat(),
                            category = "${cell.rat.displayName}_Band${cell.band}",
                            subcategory = "TxPower${txIndex}"
                        )
                    )
                }
            }
        }

        return CategorizedChartData(
            title = "Tx Power Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Tx Power (dBm)",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
    
    /**
     * Convert network records to band chart data
     */
    fun getBandData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Bands Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Band",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()
        val bandToNumber = mutableMapOf<String, Float>()
        var bandCounter = 1f

        records.forEach { record ->
            val timeString = getTimeAsString(record.time)
            record.cells.forEach { cell ->
                if (cell.band.isNotEmpty()) {
                    val bandNumber = bandToNumber.getOrPut(cell.band) { bandCounter++ }
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeString,
                            y = bandNumber,
                            category = cell.band,
                            subcategory = cell.rat.displayName
                        )
                    )
                }
            }
        }

        return CategorizedChartData(
            title = "Bands Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Band",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
    
    /**
     * Convert network records to throughput chart data
     */
    fun getThroughputData(records: List<NetworkRecord>, isUplink: Boolean): CategorizedChartData {
        if (records.isEmpty()) {
            val direction = if (isUplink) "Uplink" else "Downlink"
            return CategorizedChartData(
                title = "$direction Throughput Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Throughput (Kb/s)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = records.map { record ->
            val timeString = getTimeAsString(record.time)
            val throughput = if (isUplink) record.ulThroughput else record.dlThroughput
            ChartDataPoint(
                x = timeString,
                y = throughput.toFloat(),
                category = if (isUplink) "Uplink" else "Downlink"
            )
        }

        val direction = if (isUplink) "Uplink" else "Downlink"
        return CategorizedChartData(
            title = "$direction Throughput Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Throughput (Kb/s)",
            dataPoints = dataPoints,
            categories = setOf(direction)
        )
    }
    
    /**
     * Convert network records to channel chart data
     */
    fun getChannelData(records: List<NetworkRecord>): CategorizedChartData {
        if (records.isEmpty()) {
            return CategorizedChartData(
                title = "Channels Over Time",
                xAxisLabel = "Time",
                yAxisLabel = "Channel (scaled)",
                dataPoints = emptyList(),
                categories = emptySet()
            )
        }

        val dataPoints = mutableListOf<ChartDataPoint>()

        records.forEach { record ->
            val timeString = getTimeAsString(record.time)
            record.cells.forEach { cell ->
                // Add DL channel data
                cell.dlChannel?.let { dlChannel ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeString,
                            y = (dlChannel / 1000).toFloat(), // Scale down for better visualization
                            category = "${cell.rat.displayName}_DL",
                            subcategory = cell.band
                        )
                    )
                }

                // Add UL channel data
                cell.ulChannel?.let { ulChannel ->
                    dataPoints.add(
                        ChartDataPoint(
                            x = timeString,
                            y = (ulChannel / 1000).toFloat(), // Scale down for better visualization
                            category = "${cell.rat.displayName}_UL",
                            subcategory = cell.band
                        )
                    )
                }
            }
        }

        return CategorizedChartData(
            title = "Channels Over Time",
            xAxisLabel = "Time",
            yAxisLabel = "Channel (scaled)",
            dataPoints = dataPoints,
            categories = dataPoints.map { it.category }.toSet(),
            subcategories = dataPoints.map { it.subcategory }.toSet()
        )
    }
}
