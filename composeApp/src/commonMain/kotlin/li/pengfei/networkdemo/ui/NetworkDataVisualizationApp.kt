package li.pengfei.networkdemo.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import li.pengfei.networkdemo.data.*
import li.pengfei.networkdemo.utils.CsvDataState
import li.pengfei.networkdemo.utils.rememberFilePicker

/**
 * Main application composable for network data visualization
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NetworkDataVisualizationApp(
    csvDataState: CsvDataState,
    modifier: Modifier = Modifier
) {
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val networkRecords = remember(csvDataState.csvContent) {
        if (csvDataState.csvContent.isNotEmpty()) {
            CsvParser().parseNetworkData(csvDataState.csvContent)
        } else {
            emptyList()
        }
    }

    Column(modifier = modifier.fillMaxSize()) {
        // Top bar with file controls
        TopAppBar(
            title = { Text("Network Data Visualization") },
            actions = {
                val filePicker = rememberFilePicker { filePath ->
                    csvDataState.loadFromFile(filePath)
                }
                
                Button(
                    onClick = filePicker,
                    modifier = Modifier.padding(horizontal = 8.dp)
                ) {
                    Text("Load CSV")
                }
                
                Button(
                    onClick = csvDataState.resetToDefault,
                    modifier = Modifier.padding(horizontal = 8.dp)
                ) {
                    Text("Reset")
                }
            }
        )

        // Loading and error states
        when {
            csvDataState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
                return@Column
            }
            csvDataState.error != null -> {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
                ) {
                    Text(
                        text = "Error: ${csvDataState.error}",
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }

        // Tab row for different visualization types
        TabRow(
            selectedTabIndex = selectedTabIndex,
            modifier = Modifier.fillMaxWidth()
        ) {
            val tabs = listOf("Scatter", "Histogram", "PDF", "CDF")
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTabIndex == index,
                    onClick = { selectedTabIndex = index },
                    text = { Text(title) }
                )
            }
        }

        // Content area for charts
        if (networkRecords.isNotEmpty()) {
            when (selectedTabIndex) {
                0 -> ScatterPlotsTab(networkRecords)
                1 -> HistogramTab(networkRecords)
                2 -> PDFTab(networkRecords)
                3 -> CDFTab(networkRecords)
            }
        } else {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No data available. Please load a CSV file.",
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        }
    }
}

/**
 * Tab content for scatter plots (original data)
 */
@Composable
private fun ScatterPlotsTab(networkRecords: List<NetworkRecord>) {
    val chartDataList = remember(networkRecords) {
        generateAllChartData(networkRecords)
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Scatter Plots - Original Data",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
        }

        items(chartDataList) { chartData ->
            ChartCard(
                title = chartData.title,
                chartData = chartData,
                chartType = ChartType.SCATTER
            )
        }
    }
}

/**
 * Tab content for histogram visualization
 */
@Composable
private fun HistogramTab(networkRecords: List<NetworkRecord>) {
    val chartDataList = remember(networkRecords) {
        generateAllChartData(networkRecords)
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Histograms - Frequency Distribution",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
        }

        items(chartDataList) { chartData ->
            val statisticalData = remember(chartData) {
                chartData.toStatisticalData()
            }
            val histogramData = remember(statisticalData) {
                statisticalData.toChartData(StatisticalType.HISTOGRAM)
            }

            ChartCard(
                title = "Histogram - ${chartData.title}",
                chartData = histogramData,
                chartType = ChartType.HISTOGRAM
            )
        }
    }
}

/**
 * Tab content for PDF visualization
 */
@Composable
private fun PDFTab(networkRecords: List<NetworkRecord>) {
    val chartDataList = remember(networkRecords) {
        generateAllChartData(networkRecords)
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Probability Density Functions (PDF)",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
        }

        items(chartDataList) { chartData ->
            val statisticalData = remember(chartData) {
                chartData.toStatisticalData()
            }
            val pdfData = remember(statisticalData) {
                statisticalData.toChartData(StatisticalType.PDF)
            }

            ChartCard(
                title = "PDF - ${chartData.title}",
                chartData = pdfData,
                chartType = ChartType.LINE
            )
        }
    }
}

/**
 * Tab content for CDF visualization
 */
@Composable
private fun CDFTab(networkRecords: List<NetworkRecord>) {
    val chartDataList = remember(networkRecords) {
        generateAllChartData(networkRecords)
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Cumulative Distribution Functions (CDF)",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
        }

        items(chartDataList) { chartData ->
            val statisticalData = remember(chartData) {
                chartData.toStatisticalData()
            }
            val cdfData = remember(statisticalData) {
                statisticalData.toChartData(StatisticalType.CDF)
            }

            ChartCard(
                title = "CDF - ${chartData.title}",
                chartData = cdfData,
                chartType = ChartType.LINE
            )
        }
    }
}

/**
 * Generate all 7 types of chart data from network records
 */
private fun generateAllChartData(networkRecords: List<NetworkRecord>): List<CategorizedChartData> {
    val dataProcessor = ChartDataProcessor()
    return dataProcessor.generateAllEnhancedChartData(networkRecords)
}

/**
 * Enum for different chart types
 */
enum class ChartType {
    SCATTER,
    HISTOGRAM,
    LINE
}
